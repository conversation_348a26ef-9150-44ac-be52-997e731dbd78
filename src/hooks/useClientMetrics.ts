import { useState, useEffect } from 'react'
import { supabase, mapClientMetricToAppointment } from '@/lib/supabase'
import { useAuth } from '@/contexts/AuthContext'
import { Appointment } from '@/data/mockData'

export const useClientMetrics = () => {
  const [appointments, setAppointments] = useState<Appointment[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { profile } = useAuth()

  const fetchMetrics = async () => {
    try {
      setLoading(true)
      setError(null)

      if (!profile) {
        setAppointments([])
        return
      }

      let query = supabase.from('client_metrics_raw').select('*')

      // Filter by company_id for non-super admin users
      if (profile.role !== 'super_admin' && profile.company_id) {
        query = query.eq('company_id', profile.company_id)
      }

      const { data, error: fetchError } = await query

      if (fetchError) {
        throw fetchError
      }

      // Map the database records to our appointment interface
      const mappedAppointments = (data || []).map(mapClientMetricToAppointment)
      setAppointments(mappedAppointments)
    } catch (err: any) {
      console.error('Error fetching client metrics:', err)
      setError(err.message)
      setAppointments([])
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchMetrics()
  }, [profile])

  return {
    appointments,
    loading,
    error,
    refetch: fetchMetrics
  }
}
