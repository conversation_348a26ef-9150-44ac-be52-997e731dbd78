import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
})

// Database types
export interface Database {
  public: {
    Tables: {
      companies: {
        Row: {
          id: string
          name: string
          company_id: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          company_id: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          company_id?: string
          created_at?: string
          updated_at?: string
        }
      }
      user_profiles: {
        Row: {
          id: string
          user_id: string
          company_id: string | null
          role: 'super_admin' | 'admin' | 'user'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          company_id?: string | null
          role?: 'super_admin' | 'admin' | 'user'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          company_id?: string | null
          role?: 'super_admin' | 'admin' | 'user'
          created_at?: string
          updated_at?: string
        }
      }
      client_metrics_raw: {
        Row: {
          id: string
          client_name: string | null
          company_id: string | null
          name: string
          closer_name: string
          booked_for: string
          confirmation_disposition: string
          note: string
          phone_number: string
          address: string
          setter_name: string
          setter_number: string
          email: string
          disposition_date: string
          site_survey: string
          m1_commission: number
          m2_commission: number
          contact_link: string
          recording_media_link: string
          credit_score: number
          roof_type: string
          existing_solar: boolean
          shading: string
          appointment_type: string
          confirmed: boolean
          contact_ID: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          client_name?: string | null
          company_id?: string | null
          name: string
          closer_name: string
          booked_for: string
          confirmation_disposition: string
          note: string
          phone_number: string
          address: string
          setter_name: string
          setter_number: string
          email: string
          disposition_date: string
          site_survey: string
          m1_commission: number
          m2_commission: number
          contact_link: string
          recording_media_link: string
          credit_score: number
          roof_type: string
          existing_solar: boolean
          shading: string
          appointment_type: string
          confirmed: boolean
          contact_ID: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          client_name?: string | null
          company_id?: string | null
          name?: string
          closer_name?: string
          booked_for?: string
          confirmation_disposition?: string
          note?: string
          phone_number?: string
          address?: string
          setter_name?: string
          setter_number?: string
          email?: string
          disposition_date?: string
          site_survey?: string
          m1_commission?: number
          m2_commission?: number
          contact_link?: string
          recording_media_link?: string
          credit_score?: number
          roof_type?: string
          existing_solar?: boolean
          shading?: string
          appointment_type?: string
          confirmed?: boolean
          contact_ID?: string
          created_at?: string
          updated_at?: string
        }
      }
    }
  }
}

export type Company = Database['public']['Tables']['companies']['Row']
export type UserProfile = Database['public']['Tables']['user_profiles']['Row']
export type ClientMetric = Database['public']['Tables']['client_metrics_raw']['Row']

// Helper function to map database columns to our interface
export const mapClientMetricToAppointment = (metric: any) => {
  return {
    name: metric['Lead Name'] || '',
    closer_name: metric["Closer's Name"] || '',
    booked_for: metric['Booked For'] || '',
    confirmation_disposition: metric['Disposition'] || 'Pending',
    note: metric['Notes'] || '',
    phone_number: metric['Phone Number']?.toString() || '',
    address: metric['Address'] || '',
    setter_name: metric['Setter Name'] || '',
    setter_number: metric['Setter Number'] || '',
    email: metric['Email'] || '',
    disposition_date: metric['Disposition date'] || '',
    site_survey: metric['Site Survey'] || '',
    m1_commission: parseFloat(metric['M1 Commision']) || 0,
    m2_commission: parseFloat(metric['M2 Commision']) || 0,
    contact_link: metric['Contact Link'] || '',
    recording_media_link: metric['Media Link'] || '',
    credit_score: parseInt(metric['Credit Score']) || 0,
    roof_type: metric['Roof Type'] || '',
    existing_solar: metric['Existing Solar'] === 'Yes',
    shading: metric['Shading'] || '',
    appointment_type: metric['Appointment Type'] || '',
    confirmed: metric['Confirmed'] === 'Yes',
    contact_ID: metric['Contact ID'] || '',
    client_name: metric.client_name || '',
    company_id: metric.company_id || ''
  }
}
